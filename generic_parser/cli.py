#!/usr/bin/env python3
import argparse, pprint, sys
from generic_parser.core.graph import run_graph

def main(argv=None):
    ap = argparse.ArgumentParser(description="Generic-Parser CLI demo")
    ap.add_argument("prompt", help="Natural-language request")
    ap.add_argument("--session-id", required=True, help="Existing sandbox session UUID")
    ap.add_argument("--partner", default="oksigen")
    args = ap.parse_args(argv)

    result = run_graph(args.prompt, session_id=args.session_id, partner=args.partner)
    pprint.pp(result)

if __name__ == "__main__":
    sys.exit(main()) 