from __future__ import annotations

from dataclasses import dataclass
from typing import Annotated, Any, List, TypedDict
import operator


@dataclass(frozen=True, slots=True)
class InputData:
    """Metadata describing a CSV pushed to the sandbox."""
    variable_name: str
    data_path: str
    data_description: str
    sandbox_file_name: str


class GenericParserState(TypedDict):
    """
    State container consumed by the LangGraph workflow.
    The Annotated[...] hints tell LangGraph how to merge values between nodes
    (here we always *append* to the running list with operator.add).
    """
    user_request: Annotated[List[str], operator.add]
    generated_request: Annotated[List[Any], operator.add]
    response_data: Annotated[List[Any], operator.add]
    requests_error: Annotated[List[str], operator.add]
    input_data: Annotated[List[InputData], operator.add]
    session_id: str
    partner: str 