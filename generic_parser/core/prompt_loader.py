from __future__ import annotations

from datetime import date
from pathlib import Path

from jinja2 import Environment, FileSystemLoader, select_autoescape

_BASE_DIR = Path(__file__).resolve().parent.parent
_PROMPT_DIR = _BASE_DIR / "prompts"

env = Environment(
    loader=FileSystemLoader(_PROMPT_DIR),
    autoescape=select_autoescape(enabled_extensions=("md",)),
    trim_blocks=True,
    lstrip_blocks=True,
)


def _template_for_partner(partner: str):
    file_name = f"{partner}.md"
    if (_PROMPT_DIR / file_name).exists():
        return env.get_template(file_name)
    return env.get_template("default.md")


def render_prompt(partner: str, user_request: str, **extra) -> str:
    """
    Return a fully-rendered prompt string.

    Jinja variables available inside templates:
        - today (date)
        - user_request (str)
        - any `extra` kwargs supplied here
    """
    tmpl = _template_for_partner(partner)
    ctx = {"today": date.today(), "user_request": user_request, **extra}
    return tmpl.render(ctx) 