from __future__ import annotations
import json, re
from typing import Any, List

from .llms import get_llm_model
from .prompt_loader import render_prompt


def _clean_json_block(raw: str) -> str:
    txt = raw.strip().removeprefix("```json").removeprefix("```")
    return txt.rstrip("```").strip()


def generate_request(user_request: str,
                     errors: List[str] | None = None,
                     previous_request: Any | None = None,
                     *,
                     partner: str = "oksigen") -> dict:
    """Return parsed JSON or {'error': msg}."""
    errors = errors or []
    prompt = render_prompt(partner, user_request, errors=errors, previous_request=previous_request)

    llm = get_llm_model()
    raw = llm.invoke(prompt).content
    cleaned = _clean_json_block(raw)

    try:
        return json.loads(cleaned)
    except json.JSONDecodeError:
        # try to salvage embedded JSON
        match = re.search(r"{.*}", cleaned, flags=re.S)
        if match:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                pass
        return {"error": "LLM returned unparsable JSON"} 