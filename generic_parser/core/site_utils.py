from typing import List
import httpx


def get_data_by_index(partner: str, index: str) -> List[dict]:
    """
    Paginated scroll of Energisme "parameter/{index}/_search".

    Returns a flat list of records. Silent retry / auth handling can be added later.
    """
    page: int = 0
    acc: List[dict] = []
    while True:
        page += 1
        body = {
            "partner.code": partner,
            "paging": {"page": page, "size": 10_000},
        }
        resp = httpx.post(f"https://backend-api-stg.energisme.net/parameter/{index}/_search",
                          json=body, timeout=60)
        resp.raise_for_status()
        payload = resp.json()

        data, cur, total = payload.get("data", []), payload.get("currentCount", 0), payload.get("totalCount", 0)
        acc.extend(data)
        if cur == 0 or cur >= total:
            break
    return acc


def update_json_with_sites(request_json: dict, sites: list[str]) -> dict:
    """Replace wildcards (`all`, `-`) in JSON with actual site lists."""
    def _resolve(lst: list[str]):
        if lst == ["all"]:
            return sites
        if lst and lst[0] == "-":
            return [s for s in sites if s not in lst[1:]]
        return lst

    if "group_id" in request_json:
        request_json["group_id"] = _resolve(request_json["group_id"])

    if "perimeter.list" in request_json:
        request_json["perimeter.list"] = _resolve(request_json["perimeter.list"])
    elif "perimeter" in request_json and "list" in request_json["perimeter"]:
        request_json["perimeter"]["list"] = _resolve(request_json["perimeter"]["list"])

    return request_json