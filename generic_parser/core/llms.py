import json, os
from functools import lru_cache
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic
from langchain_deepseek import ChatDeepSeek


def _load_cfg(cfg_path: str) -> tuple[str, str]:
    """Read provider/model from JSON; fall back to sensible defaults."""
    provider, model = "gemini", "gemini-2.0-flash"
    if os.path.exists(cfg_path):
        try:
            with open(cfg_path) as fh:
                raw = json.load(fh).get("generic", {})
                provider, model = raw.get("provider", provider), raw.get("model", model)
        except Exception:  # noqa: BLE001
            pass
    return provider, model


_PROVIDER_MAP = {
    "gemini": ChatGoogleGenerativeAI,
    "openai": ChatOpenAI,
    "anthropic": ChatAnthropic,
    "deepseek": ChatDeepSeek,
}


@lru_cache(maxsize=4)
def get_llm_model(cfg_path: str = "config/model_config.json"):
    """Factory that returns a *cached* LangChain chat model."""
    provider, model = _load_cfg(cfg_path)
    model_cls = _PROVIDER_MAP.get(provider, ChatAnthropic)
    return model_cls(model=model, temperature=0) 