from __future__ import annotations

import os, requests
from datetime import datetime

from .csv_utils import generate_details_csv
from .site_utils import update_json_with_sites, get_data_by_index

_GP_ENDPOINT = "http://11.41.97.4:8065/api/generic-parser"

_ERROR_MAP = {
    "GP_1.1": "MANDATORY_FIELD_EMPTY",
    "GP_1.2": "INVALID_PERIMETER_TYPE",
    "GP_1.10": "INVALID_PERIMETER_LIST",
    "GP_1.3": "INVALID_DATE_FORMAT",
    "GP_1.4": "START_DATE_MUST_BE_BEFORE_END_DATE",
    "GP_1.5": "INVALID_GROUP_BY",
    "GP_1.6": "INVALID_GROUP_ID",
    "GP_1.7": "INVALID_COMPARISON_PERIOD",
}


def process_generic_parser_request(request_json: dict, *, partner: str, session_id: str):
    """Send request to GP API, map errors, save CSV and upload to sandbox."""
    if not session_id:
        raise ValueError("session_id required")

    sites = [s["code"] for s in get_data_by_index(partner, "site")]
    req = update_json_with_sites(request_json.copy(), sites)
    req["partner.code"] = partner

    try:
        r = requests.post(_GP_ENDPOINT, json=req, timeout=90)
    except requests.RequestException as exc:
        return {"status": "CONNECTION_ERROR", "detail": str(exc)}

    if r.status_code != 200:
        return {"status": "SERVER_ERROR", "detail": f"HTTP {r.status_code}"}

    try:
        payload = r.json()
    except ValueError:
        return "INVALID_REQUEST"

    if (err := payload.get("error")):
        return _ERROR_MAP.get(err, "INVALID_REQUEST")

    if not payload.get("details"):
        return "EMPTY_DETAILS"

    # success → create CSV
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_base = f"response_{ts}"
    local_csv, sandbox_path = generate_details_csv(payload, "uploads", csv_base, session_id)

    return {
        "input_data": payload,
        "csv_path": local_csv,
        "data_file_name": csv_base,
        "sandbox_file_name": sandbox_path,
    } 