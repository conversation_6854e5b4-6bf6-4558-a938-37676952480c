from __future__ import annotations
import operator
from typing import Any, List, TypedDict

from langgraph.graph import StateGraph, START, END

from .models import GenericParserState, InputData
from .request_builder import generate_request
from .api import process_generic_parser_request


# ─────────────────────────── nodes ────────────────────────────
def _node_generate(state: GenericParserState) -> GenericParserState:  # type: ignore[override]
    req = generate_request(
        user_request=state["user_request"][-1],
        errors=state["requests_error"],
        previous_request=state["generated_request"][-1] if state["generated_request"] else None,
        partner=state["partner"],
    )
    if "error" in req:
        state["requests_error"].append(req["error"])
    else:
        state["generated_request"].append(req)
    return state


def _node_process(state: GenericParserState) -> GenericParserState:  # type: ignore[override]
    if not state["generated_request"]:
        state["requests_error"].append("No generated request")
        return state

    result = process_generic_parser_request(
        state["generated_request"][-1],
        partner=state["partner"],
        session_id=state["session_id"],
    )

    # error string → retry
    if isinstance(result, str):
        state["requests_error"].append(result)
        return state

    # dict with status → fatal
    if "status" in result:
        state["requests_error"].append(f"{result['status']}: {result.get('detail')}")
        return state

    # success
    state["response_data"].append(result)
    state["input_data"].append(
        InputData(
            variable_name=result["data_file_name"],
            data_path=result["csv_path"],
            data_description=state["user_request"][-1],
            sandbox_file_name=result["sandbox_file_name"],
        )
    )
    return state


# ─────────────────────────── graph builder ─────────────────────
def create_generic_parser_graph():
    builder = StateGraph(GenericParserState)
    builder.add_node("generate", _node_generate)
    builder.add_node("process", _node_process)

    builder.add_edge(START, "generate")
    builder.add_edge("generate", "process")

    def _route(state: GenericParserState):  # noqa: ANN001
        return END if len(state["requests_error"]) >= 4 or not state["requests_error"] else "generate"

    builder.add_conditional_edges("process", _route)
    return builder.compile()


def run_graph(user_request: str, session_id: str, partner: str = "oksigen"):
    graph = create_generic_parser_graph()
    init = {
        "user_request": [user_request],
        "generated_request": [],
        "response_data": [],
        "requests_error": [],
        "input_data": [],
        "session_id": session_id,
        "partner": partner,
    }
    return graph.invoke(init) 