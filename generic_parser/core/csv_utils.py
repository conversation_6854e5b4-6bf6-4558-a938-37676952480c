from __future__ import annotations

import csv, os
from pathlib import Path
from datetime import datetime
from typing import <PERSON>ple, Any

import asyncio

from sandbox_client import SandboxClient


async def _upload(session_id: str, local_csv: Path, var_name: str) -> str:
    client = SandboxClient()
    await client.create_session(session_id)
    await client.upload_file(session_id=session_id,
                             file_path=str(local_csv),
                             variable_name=var_name)

    # path convention used by Energisme sandbox
    return f"/tmp/sessions/{session_id}/files/{local_csv.name}"


def generate_details_csv(data: dict[str, Any], dest_dir: str, base_name: str,
                         session_id: str) -> Tuple[str, str]:
    """
    Write `details` array to CSV and push it to the sandbox.
    Returns (local_path, sandbox_absolute_path).
    """
    details = data.get("details", [])
    if not details:
        raise ValueError("No 'details' array to serialise.")

    field_names = sorted({k for d in details for k in d if k != "indicators"})
    indicator_keys = sorted({i['key'] for d in details for i in d.get("indicators", []) if 'key' in i})
    headers = field_names + indicator_keys

    rows = []
    for d in details:
        row = {f: d.get(f, "") for f in field_names}
        for ind in d.get("indicators", []):
            row[ind["key"]] = ind.get("value", "")
        rows.append(row)

    Path(dest_dir).mkdir(parents=True, exist_ok=True)
    local_csv = Path(dest_dir) / f"{base_name}.csv"
    with local_csv.open("w", newline="", encoding="utf-8") as fh:
        w = csv.DictWriter(fh, fieldnames=headers)
        w.writeheader()
        w.writerows(rows)

    sandbox_path = asyncio.run(_upload(session_id, local_csv, base_name))
    return str(local_csv), sandbox_path 