How to create an Indicator formula:

- **Basic Structure:**
The formula must follow the structure:
{{algorithm_id#mode:algorithm_indicator_id}}
Where:
- **algorithm_id** is a technical identifier (appears between {{ and #).
- **mode** is either in or out (appears between # and :).
- **algorithm_indicator_id** comes after the : and before }}.

- **Aggregation of Multiple Indicators:**
When using the same algorithm_id, you can aggregate the results from multiple algorithm_indicator_id values. To do this, simply combine them in the same formula using mathematical operators.
**For example:**
To aggregate two sensor readings, you can write:
{{sensor#in:OY4U4HwBCeLaNCYRERoi}} + {{sensor#in:AB2U5HwBfjv8CYREthg}}
This instructs the system to aggregate the values from both sensor indicators.

- **Operands:**
You can include standard mathematical operations in your formulas, such as addition (+), subtraction (-), multiplication (*), and division (/).
**Examples:**
{{site#out:site.custom_field_float_3}} / 3 +  {{site#out:site.custom_field_float_1}}
{{sensor#in:OY4U4HwBCeLaNCYRERoi}} + {{sensor#in:AB2U5HwBfjv8CYREthg}}

Here are the available algorithm_ids along with their descriptions:

- **algorithm_id: sum_sensor_labels**
Description: Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, water, etc.).
- **Mode:** "in"
- **algorithm_indicator_id options:**
    - energie_facturante_grdf (to get the electric consumption data of "grdf" meters)
    - conso_mensuelle_enedis (to get the electric consumption data of "enedis" meters)

- **algorithm_id: load_curve**
Description: Uses the load curve service to get the detailed time series of electric consumption data for few or one site and use it to get hight granurarity data like 1 hour or less.
- **Mode:** "out"
- **algorithm_indicator_id options:**
    - total-power_supply (to get time series of the total power used by the site for each timestamp)

- **algorithm_id: site**
Description: Uses the site service to get the site properies like total area, typology,...
- **Mode:** "out"
- **algorithm_indicator_id options:**
    - site.total_area_m2 (to get total area of the site in square meter)
    - site.typology (to get the typology of the site) 